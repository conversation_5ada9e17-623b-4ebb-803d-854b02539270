<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<application xmlns="http://ns.adobe.com/air/application/3.2">
  <id>KhaleelM.ShuttleFTL</id>
  <versionNumber>1.0</versionNumber>
  <filename>Shuttle FTL</filename>
  <description>A computer game by Khaleel M.</description>
  <!-- To localize the description, use the following format for the description element.<description><text xml:lang="en">English App description goes here</text><text xml:lang="fr">French App description goes here</text><text xml:lang="ja">Japanese App description goes here</text></description>-->
  <name>Shuttle FTL</name>
  <!-- To localize the name, use the following format for the name element.<name><text xml:lang="en">English App name goes here</text><text xml:lang="fr">French App name goes here</text><text xml:lang="ja">Japanese App name goes here</text></name>-->
  <copyright>© Khaleel M</copyright>
  <initialWindow>
    <content>Shuttle%20FTL.swf</content>
    <systemChrome>standard</systemChrome>
    <transparent>false</transparent>
    <visible>true</visible>
    <fullScreen>false</fullScreen>
    <aspectRatio>portrait</aspectRatio>
    <renderMode>auto</renderMode>
    <width>600</width>
    <height>800</height>
    <minSize>300 400</minSize>
    <maximizable>true</maximizable>
    <minimizable>true</minimizable>
    <resizable>true</resizable>
  </initialWindow>
  <icon>
    <image16x16>Application%20Icon/AppIcon16.png</image16x16>
    <image32x32>Application%20Icon/AppIcon32.png</image32x32>
    <image48x48>Application%20Icon/AppIcon48.png</image48x48>
    <image128x128>Application%20Icon/AppIcon128.png</image128x128>
  </icon>
  <customUpdateUI>false</customUpdateUI>
  <allowBrowserInvocation>false</allowBrowserInvocation>
  <installFolder>Shuttle FTL</installFolder>
  <programMenuFolder>Shuttle FTL</programMenuFolder>
  <supportedProfiles>desktop extendedDesktop </supportedProfiles>
</application>
