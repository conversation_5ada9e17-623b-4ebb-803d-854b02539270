# Shuttle FTL
The ultimate space travel.

A mobile game made with Adobe Flash, as a part of my university project.

Play Shuttle FTL on itch.io: https://khaleelmuhd1998.itch.io/shuttleftl

## Instructions
- Version 1.0 and below - Use Adobe Flash Professional CS6, Adobe AIR 3.2 and Adobe Flash Player 11.2 to open and modify the project.
- Version 1.1 to 1.2 - Adobe Animate CC 2020 and Adobe AIR SDK 33 from HARMAN (https://airsdk.harman.com/) were used to facilitate building for Google Android and Google Chrome OS. The code/scripts remain similar to Version 1.0.
- Version 1.3 and above - The game is completely reworked. Use Adobe Animate CC 2020, Adobe Flash Player 32 and Adobe AIR SDK 51 from HARMAN (https://airsdk.harman.com/) to open and modify the project.

## Software Tools
- Adobe Animate CC 2020: https://www.adobe.com/products/animate.html
- Adobe AIR SDK 51 from HARMAN: https://airsdk.harman.com/
- Adobe Flash Player 32
- Ruffle: https://www.ruffle.rs/
- Paint.NET: https://www.getpaint.net/
- Audacity: https://www.audacityteam.org/

## Support
Join my Discord server to ask questions or get support with this project: https://discord.gg/xvGXw22PvP
