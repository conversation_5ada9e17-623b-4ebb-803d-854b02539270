# Ruffle

Ruffle is licensed under either of

- Apache License, Version 2.0 (http://www.apache.org/licenses/LICENSE-2.0)
- MIT license (http://opensource.org/licenses/MIT)

at your option.

## MIT License

Copyright (c) 2018-2022 Ruffle LLC <<EMAIL>> and Ruffle contributors
                        (https://github.com/ruffle-rs/ruffle/graphs/contributors)

Permission is hereby granted, free of charge, to any
person obtaining a copy of this software and associated
documentation files (the "Software"), to deal in the
Software without restriction, including without
limitation the rights to use, copy, modify, merge,
publish, distribute, sublicense, and/or sell copies of
the Software, and to permit persons to whom the Software
is furnished to do so, subject to the following
conditions:

The above copyright notice and this permission notice
shall be included in all copies or substantial portions
of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF
ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED
TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT
SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR
IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

## Apache License, Version 2.0

                              Apache License
                        Version 2.0, January 2004
                     http://www.apache.org/licenses/

TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

1. Definitions.

   "License" shall mean the terms and conditions for use, reproduction,
   and distribution as defined by Sections 1 through 9 of this document.

   "Licensor" shall mean the copyright owner or entity authorized by
   the copyright owner that is granting the License.

   "Legal Entity" shall mean the union of the acting entity and all
   other entities that control, are controlled by, or are under common
   control with that entity. For the purposes of this definition,
   "control" means (i) the power, direct or indirect, to cause the
   direction or management of such entity, whether by contract or
   otherwise, or (ii) ownership of fifty percent (50%) or more of the
   outstanding shares, or (iii) beneficial ownership of such entity.

   "You" (or "Your") shall mean an individual or Legal Entity
   exercising permissions granted by this License.

   "Source" form shall mean the preferred form for making modifications,
   including but not limited to software source code, documentation
   source, and configuration files.

   "Object" form shall mean any form resulting from mechanical
   transformation or translation of a Source form, including but
   not limited to compiled object code, generated documentation,
   and conversions to other media types.

   "Work" shall mean the work of authorship, whether in Source or
   Object form, made available under the License, as indicated by a
   copyright notice that is included in or attached to the work
   (an example is provided in the Appendix below).

   "Derivative Works" shall mean any work, whether in Source or Object
   form, that is based on (or derived from) the Work and for which the
   editorial revisions, annotations, elaborations, or other modifications
   represent, as a whole, an original work of authorship. For the purposes
   of this License, Derivative Works shall not include works that remain
   separable from, or merely link (or bind by name) to the interfaces of,
   the Work and Derivative Works thereof.

   "Contribution" shall mean any work of authorship, including
   the original version of the Work and any modifications or additions
   to that Work or Derivative Works thereof, that is intentionally
   submitted to Licensor for inclusion in the Work by the copyright owner
   or by an individual or Legal Entity authorized to submit on behalf of
   the copyright owner. For the purposes of this definition, "submitted"
   means any form of electronic, verbal, or written communication sent
   to the Licensor or its representatives, including but not limited to
   communication on electronic mailing lists, source code control systems,
   and issue tracking systems that are managed by, or on behalf of, the
   Licensor for the purpose of discussing and improving the Work, but
   excluding communication that is conspicuously marked or otherwise
   designated in writing by the copyright owner as "Not a Contribution."

   "Contributor" shall mean Licensor and any individual or Legal Entity
   on behalf of whom a Contribution has been received by Licensor and
   subsequently incorporated within the Work.

2. Grant of Copyright License. Subject to the terms and conditions of
   this License, each Contributor hereby grants to You a perpetual,
   worldwide, non-exclusive, no-charge, royalty-free, irrevocable
   copyright license to reproduce, prepare Derivative Works of,
   publicly display, publicly perform, sublicense, and distribute the
   Work and such Derivative Works in Source or Object form.

3. Grant of Patent License. Subject to the terms and conditions of
   this License, each Contributor hereby grants to You a perpetual,
   worldwide, non-exclusive, no-charge, royalty-free, irrevocable
   (except as stated in this section) patent license to make, have made,
   use, offer to sell, sell, import, and otherwise transfer the Work,
   where such license applies only to those patent claims licensable
   by such Contributor that are necessarily infringed by their
   Contribution(s) alone or by combination of their Contribution(s)
   with the Work to which such Contribution(s) was submitted. If You
   institute patent litigation against any entity (including a
   cross-claim or counterclaim in a lawsuit) alleging that the Work
   or a Contribution incorporated within the Work constitutes direct
   or contributory patent infringement, then any patent licenses
   granted to You under this License for that Work shall terminate
   as of the date such litigation is filed.

4. Redistribution. You may reproduce and distribute copies of the
   Work or Derivative Works thereof in any medium, with or without
   modifications, and in Source or Object form, provided that You
   meet the following conditions:

   (a) You must give any other recipients of the Work or
   Derivative Works a copy of this License; and

   (b) You must cause any modified files to carry prominent notices
   stating that You changed the files; and

   (c) You must retain, in the Source form of any Derivative Works
   that You distribute, all copyright, patent, trademark, and
   attribution notices from the Source form of the Work,
   excluding those notices that do not pertain to any part of
   the Derivative Works; and

   (d) If the Work includes a "NOTICE" text file as part of its
   distribution, then any Derivative Works that You distribute must
   include a readable copy of the attribution notices contained
   within such NOTICE file, excluding those notices that do not
   pertain to any part of the Derivative Works, in at least one
   of the following places: within a NOTICE text file distributed
   as part of the Derivative Works; within the Source form or
   documentation, if provided along with the Derivative Works; or,
   within a display generated by the Derivative Works, if and
   wherever such third-party notices normally appear. The contents
   of the NOTICE file are for informational purposes only and
   do not modify the License. You may add Your own attribution
   notices within Derivative Works that You distribute, alongside
   or as an addendum to the NOTICE text from the Work, provided
   that such additional attribution notices cannot be construed
   as modifying the License.

   You may add Your own copyright statement to Your modifications and
   may provide additional or different license terms and conditions
   for use, reproduction, or distribution of Your modifications, or
   for any such Derivative Works as a whole, provided Your use,
   reproduction, and distribution of the Work otherwise complies with
   the conditions stated in this License.

5. Submission of Contributions. Unless You explicitly state otherwise,
   any Contribution intentionally submitted for inclusion in the Work
   by You to the Licensor shall be under the terms and conditions of
   this License, without any additional terms or conditions.
   Notwithstanding the above, nothing herein shall supersede or modify
   the terms of any separate license agreement you may have executed
   with Licensor regarding such Contributions.

6. Trademarks. This License does not grant permission to use the trade
   names, trademarks, service marks, or product names of the Licensor,
   except as required for reasonable and customary use in describing the
   origin of the Work and reproducing the content of the NOTICE file.

7. Disclaimer of Warranty. Unless required by applicable law or
   agreed to in writing, Licensor provides the Work (and each
   Contributor provides its Contributions) on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
   implied, including, without limitation, any warranties or conditions
   of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
   PARTICULAR PURPOSE. You are solely responsible for determining the
   appropriateness of using or redistributing the Work and assume any
   risks associated with Your exercise of permissions under this License.

8. Limitation of Liability. In no event and under no legal theory,
   whether in tort (including negligence), contract, or otherwise,
   unless required by applicable law (such as deliberate and grossly
   negligent acts) or agreed to in writing, shall any Contributor be
   liable to You for damages, including any direct, indirect, special,
   incidental, or consequential damages of any character arising as a
   result of this License or out of the use or inability to use the
   Work (including but not limited to damages for loss of goodwill,
   work stoppage, computer failure or malfunction, or any and all
   other commercial damages or losses), even if such Contributor
   has been advised of the possibility of such damages.

9. Accepting Warranty or Additional Liability. While redistributing
   the Work or Derivative Works thereof, You may choose to offer,
   and charge a fee for, acceptance of support, warranty, indemnity,
   or other liability obligations and/or rights consistent with this
   License. However, in accepting such obligations, You may act only
   on Your own behalf and on Your sole responsibility, not on behalf
   of any other Contributor, and only if You agree to indemnify,
   defend, and hold each Contributor harmless for any liability
   incurred by, or claims asserted against, such Contributor by reason
   of your accepting any such warranty or additional liability.

END OF TERMS AND CONDITIONS

# Third-Party Libraries

Ruffle depends on third-party libraries with compatible licenses.

| Library Name | License | Authors/Notes |
|-|-|-|
| [ab_glyph_rasterizer](https://github.com/alexheretic/ab-glyph) | [Apache-2.0](#Apache-20) | Copyright 2020 Alex Butler  |
| [addr2line](https://github.com/gimli-rs/addr2line) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016-2018 The gimli Developers  |
| [adler](https://github.com/jonas-schievink/adler.git) | [0BSD](#0BSD)/[Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Jonas Schievink <<EMAIL>> |
| [adler32](https://github.com/remram44/adler32-rs) | [Zlib](#Zlib) | Copyright (c) 2016 Remi Rampin and adler32-rs contributors (c) 1995-2017 Jean-loup Gailly and Mark Adler |
| [ahash](https://github.com/tkaitchuck/ahash) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Amanieu d'Antras  |
| [aho-corasick](https://github.com/BurntSushi/aho-corasick) | [MIT](#MIT)/[Unlicense](#Unlicense) | Copyright (c) 2015 Andrew Gallant  |
| [alsa](https://github.com/diwic/alsa-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) David Henningsson <<EMAIL>> |
| [alsa-sys](https://github.com/diwic/alsa-sys) | [MIT](#MIT) | Copyright (c) 2018 diwic  |
| [andrew](https://github.com/Smithay/andrew) | [MIT](#MIT) | Copyright (c) 2018 Lucas Timmins  |
| ansi_term | [MIT](#MIT) | Copyright (c) <EMAIL>, Ryan Scheel (Havvy) <<EMAIL>>, Josh Triplett <<EMAIL>> |
| [approx](https://github.com/brendanzab/approx) | [Apache-2.0](#Apache-20) | Copyright (c) Brendan Zabarauskas <<EMAIL>> |
| [array-macro](https://gitlab.com/KonradBorowski/array-macro) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Konrad Borowski  |
| [arrayref](https://github.com/droundy/arrayref) | [BSD-2-Clause](#BSD-2-Clause) | Copyright (c) 2015 David Roundy <<EMAIL>>  |
| [arrayvec](https://github.com/bluss/arrayvec) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Ulrik Sverdrup "bluss" 2015-2017  |
| [ascii](https://github.com/tomprogrammer/rust-ascii) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Thomas Bahn and contributors Copyright (c) 2014 The Rust Project Developers  |
| [ash](https://github.com/MaikKlein/ash) | [MIT](#MIT) | Copyright (c) 2016 ASH  |
| [atty](https://github.com/softprops/atty) | [MIT](#MIT) | Copyright (c) 2015-2019 Doug Tangren  |
| [autocfg](https://github.com/cuviper/autocfg) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018 Josh Stone  |
| [backtrace](https://github.com/rust-lang/backtrace-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [base-x](https://github.com/OrKoN/base-x-rs) | [MIT](#MIT) | Copyright base-x contributors and Oleksii Rudenko (c) 2016  |
| [base64](https://github.com/marshallpierce/rust-base64) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 Alice Maz  |
| [bindgen](https://github.com/rust-lang/rust-bindgen) | [BSD-3-Clause](#BSD-3-Clause) | Copyright (c) 2013, Jyun-Yan You  |
| [bit-set](https://github.com/contain-rs/bit-set) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 The Rust Project Developers  |
| [bit-vec](https://github.com/contain-rs/bit-vec) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Rust Project Developers  |
| [bitflags](https://github.com/bitflags/bitflags) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [bitstream-io](https://github.com/tuffy/bitstream-io) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Brian Langenberger  |
| [blake2b_simd](https://github.com/oconnor663/blake2_simd) | [MIT](#MIT) | Copyright (c) 2018 Jack O'Connor  |
| [block](http://github.com/SSheldon/rust-block) | [MIT](#MIT) | Copyright (c) Steven Sheldon |
| [bstr](https://github.com/BurntSushi/bstr) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018-2019 Andrew Gallant  |
| [build_const](https://github.com/vitiral/build_const) | [MIT](#MIT) | Copyright (c) 2017 Garrett Berg, <EMAIL>  |
| [bumpalo](https://github.com/fitzgen/bumpalo) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 Nick Fitzgerald  |
| [bytemuck](https://github.com/Lokathor/bytemuck) | [Apache-2.0](#Apache-20)/[MIT](#MIT)/[Zlib](#Zlib) | Copyright (c) 2019 Daniel "Lokathor" Gee.  |
| [byteorder](https://github.com/BurntSushi/byteorder) | [MIT](#MIT)/[Unlicense](#Unlicense) | Copyright (c) 2015 Andrew Gallant  |
| [bytes](https://github.com/tokio-rs/bytes) | [MIT](#MIT) | Copyright (c) 2018 Carl Lerche  |
| [calloop](https://github.com/Smithay/calloop) | [MIT](#MIT) | Copyright (c) 2018 Victor Berger  |
| [cc](https://github.com/alexcrichton/cc-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [cesu8](https://github.com/emk/cesu8-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Eric Kidd <*******************> |
| [cexpr](https://github.com/jethrogb/rust-cexpr) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Jethro Beekman <<EMAIL>> |
| [cfg-if](https://github.com/alexcrichton/cfg-if) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [cfg-if](https://github.com/alexcrichton/cfg-if) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [cfg_aliases](https://github.com/katharostech/cfg_aliases) | [MIT](#MIT) | Copyright (c) 2020 Katharos Technology  |
| [chrono](https://github.com/chronotope/chrono) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014, Kang Seonghoon.  |
| [chunked_transfer](https://github.com/frewsxcv/rust-chunked-transfer) | [Apache-2.0](#Apache-20) | Copyright (c) Corey Farwell <<EMAIL>> |
| [clang-sys](https://github.com/KyleMayes/clang-sys) | [Apache-2.0](#Apache-20) | Copyright (c) Kyle Mayes <<EMAIL>> |
| [clap](https://github.com/clap-rs/clap) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015-2016 Kevin B. Knapp  |
| [clap_derive](https://github.com/clap-rs/clap/tree/master/clap_derive) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Guillaume Pinot <<EMAIL>>, Clap Maintainers |
| [clipboard](https://github.com/aweinstock314/rust-clipboard) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Avi Weinstock <<EMAIL>> |
| [clipboard-win](https://github.com/DoumanAsh/clipboard-win) | [MIT](#MIT) | Copyright (c) 2015 Douman  |
| [cocoa](https://github.com/servo/core-foundation-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2012-2013 Mozilla Foundation  |
| [cocoa-foundation](https://github.com/servo/core-foundation-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2012-2013 Mozilla Foundation  |
| [color_quant](https://github.com/image-rs/color_quant.git) | [MIT](#MIT) | Copyright (c) 2016 PistonDevelopers  |
| [combine](https://github.com/Marwes/combine) | [MIT](#MIT) | Copyright (c) 2015 Markus Westerlind  |
| [console](https://github.com/mitsuhiko/console) | [MIT](#MIT) | Copyright (c) 2017 Armin Ronacher <<EMAIL>>  |
| [console_error_panic_hook](https://github.com/rustwasm/console_error_panic_hook) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018 Nick Fitzgerald  |
| [console_log](https://github.com/iamcodemaker/console_log) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018 Matthew Nicholson  |
| [const_fn](https://github.com/taiki-e/const_fn) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Taiki Endo <<EMAIL>> |
| [constant_time_eq](https://github.com/cesarb/constant_time_eq) | [CC0-1.0](#CC0-10) | Copyright (c) Cesar Eduardo Barros <<EMAIL>> |
| [cookie](https://github.com/SergioBenitez/cookie-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Sergio Benitez Copyright (c) 2014 Alex Crichton  |
| [cookie_store](https://github.com/pfernie/cookie_store) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017  |
| [copyless](https://github.com/kvark/copyless) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Nikolay Kim  |
| [core-foundation](https://github.com/servo/core-foundation-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2012-2013 Mozilla Foundation  |
| [core-foundation-sys](https://github.com/servo/core-foundation-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2012-2013 Mozilla Foundation  |
| [core-graphics](https://github.com/servo/core-foundation-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2012-2013 Mozilla Foundation  |
| [core-graphics-types](https://github.com/servo/core-foundation-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2012-2013 Mozilla Foundation  |
| [core-video-sys](https://github.com/luozijun/rust-core-video-sys) | [MIT](#MIT) | Copyright (c) 2018 寧靜  |
| [coreaudio-rs](https://github.com/RustAudio/coreaudio-rs.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015  |
| [coreaudio-sys](https://github.com/RustAudio/coreaudio-sys.git) | [MIT](#MIT) | Copyright (c) 2015  |
| [cpal](https://github.com/rustaudio/cpal) | [Apache-2.0](#Apache-20) | Copyright (c) The CPAL contributors, Pierre Krieger <<EMAIL>> |
| [crc](https://github.com/mrhooray/crc-rs.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 crc-rs Developers  |
| [crc32fast](https://github.com/srijs/rust-crc32fast) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018 Sam Rijs, Alex Crichton and contributors  |
| [crossbeam-channel](https://github.com/crossbeam-rs/crossbeam) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 The Crossbeam Project Developers  |
| [crossbeam-deque](https://github.com/crossbeam-rs/crossbeam) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 The Crossbeam Project Developers  |
| [crossbeam-epoch](https://github.com/crossbeam-rs/crossbeam) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 The Crossbeam Project Developers  |
| [crossbeam-utils](https://github.com/crossbeam-rs/crossbeam) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 The Crossbeam Project Developers  |
| [csv](https://github.com/BurntSushi/rust-csv) | [MIT](#MIT)/[Unlicense](#Unlicense) | Copyright (c) 2015 Andrew Gallant  |
| [csv-core](https://github.com/BurntSushi/rust-csv) | [MIT](#MIT)/[Unlicense](#Unlicense) | Copyright (c) 2015 Andrew Gallant  |
| [ctor](https://github.com/mmastrac/rust-ctor) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Matt Mastracci <<EMAIL>> |
| [curl](https://github.com/alexcrichton/curl-rust) | [MIT](#MIT) | Copyright (c) 2014 Carl Lerche  |
| [curl-sys](https://github.com/alexcrichton/curl-rust) | [MIT](#MIT) | Copyright (c) 2014 Carl Lerche  |
| [d3d12](https://github.com/gfx-rs/d3d12-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) msiglreith <<EMAIL>>, Dzmitry Malyshau <<EMAIL>> |
| [darling](https://github.com/TedDriggs/darling) | [MIT](#MIT) | Copyright (c) 2017 Ted Driggs  |
| [darling_core](https://github.com/TedDriggs/darling) | [MIT](#MIT) | Copyright (c) 2017 Ted Driggs  |
| [darling_macro](https://github.com/TedDriggs/darling) | [MIT](#MIT) | Copyright (c) 2017 Ted Driggs  |
| [dasp](https://github.com/rustaudio/dasp.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [dasp_envelope](https://github.com/rustaudio/dasp.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [dasp_frame](https://github.com/rustaudio/dasp.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [dasp_interpolate](https://github.com/rustaudio/dasp.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [dasp_peak](https://github.com/rustaudio/dasp.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [dasp_ring_buffer](https://github.com/rustaudio/dasp.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [dasp_rms](https://github.com/rustaudio/dasp.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [dasp_sample](https://github.com/rustaudio/sample.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [dasp_signal](https://github.com/rustaudio/dasp.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [dasp_slice](https://github.com/rustaudio/dasp.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [dasp_window](https://github.com/rustaudio/dasp.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 RustAudio Developers  |
| [deflate](https://github.com/image-rs/deflate-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016  |
| [derivative](https://github.com/mcarton/rust-derivative) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Martin Carton  |
| [difference](https://github.com/johannhof/difference.rs) | [MIT](#MIT) | Copyright (c) 2015 Johann Hofmann  |
| [dirs](https://github.com/soc/dirs-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018-2019 dirs-rs contributors  |
| [dirs-sys](https://github.com/dirs-dev/dirs-sys-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018-2019 dirs-rs contributors  |
| [discard](https://github.com/Pauan/rust-discard) | [MIT](#MIT) | Copyright (c) 2018  |
| [dispatch](http://github.com/SSheldon/rust-dispatch) | [MIT](#MIT) | Copyright (c) Steven Sheldon |
| [dlib](https://github.com/vberger/dlib) | [MIT](#MIT) | Copyright (c) 2015 Victor Berger  |
| [downcast-rs](https://github.com/marcianx/downcast-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2020 Ashish Myles and contributors  |
| [either](https://github.com/bluss/either) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015  |
| [embed-resource](https://github.com/nabijaczleweli/rust-embed-resource) | [MIT](#MIT) | Copyright (c) 2017 nabijaczleweli  |
| [encode_unicode](https://github.com/tormol/encode_unicode) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Torbjørn Birch Moltu <<EMAIL>> |
| [encoding_rs](https://github.com/hsivonen/encoding_rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright Mozilla Foundation  |
| [enum-map](https://gitlab.com/KonradBorowski/enum-map) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Konrad Borowski <<EMAIL>> |
| [enum-map-derive](https://gitlab.com/KonradBorowski/enum-map) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Konrad Borowski <<EMAIL>> |
| [env_logger](https://github.com/env-logger-rs/env_logger/) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [error-chain](https://github.com/rust-lang-nursery/error-chain) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 The Error-Chain Project Developers  |
| [euclid](https://github.com/servo/euclid) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2012-2013 Mozilla Foundation  |
| exporter | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Nathan Adams <<EMAIL>> |
| [fastrand](https://github.com/stjepang/fastrand) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Stjepan Glavina <<EMAIL>> |
| [fetch_unroll](https://github.com/katyo/fetch_unroll) | [Apache-2.0](#Apache-20) | Copyright (c) K. <<EMAIL>> |
| [filetime](https://github.com/alexcrichton/filetime) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [fixedbitset](https://github.com/bluss/fixedbitset) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015-2017  |
| [flate2](https://github.com/rust-lang/flate2-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [flume](https://github.com/zesterer/flume) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Joshua Barretto <<EMAIL>> |
| [fnv](https://github.com/servo/rust-fnv) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Contributors  |
| [foreign-types](https://github.com/sfackler/foreign-types) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 The foreign-types Developers  |
| [foreign-types-shared](https://github.com/sfackler/foreign-types) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 The foreign-types Developers  |
| [form_urlencoded](https://github.com/servo/rust-url) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2013-2016 The rust-url developers  |
| [fuchsia-zircon](https://fuchsia.googlesource.com/garnet/) | [BSD-3-Clause](#BSD-3-Clause) | Copyright (c) Raph Levien <<EMAIL>> |
| [fuchsia-zircon-sys](https://fuchsia.googlesource.com/garnet/) | [BSD-3-Clause](#BSD-3-Clause) | Copyright (c) Raph Levien <<EMAIL>> |
| [futures](https://github.com/rust-lang/futures-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Alex Crichton Copyright (c) 2017 The Tokio Authors  |
| [futures-channel](https://github.com/rust-lang/futures-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Alex Crichton Copyright (c) 2017 The Tokio Authors  |
| [futures-core](https://github.com/rust-lang/futures-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Alex Crichton Copyright (c) 2017 The Tokio Authors  |
| [futures-executor](https://github.com/rust-lang/futures-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Alex Crichton Copyright (c) 2017 The Tokio Authors  |
| [futures-io](https://github.com/rust-lang/futures-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Alex Crichton Copyright (c) 2017 The Tokio Authors  |
| [futures-lite](https://github.com/stjepang/futures-lite) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Stjepan Glavina <<EMAIL>>, Contributors to futures-rs |
| [futures-macro](https://github.com/rust-lang/futures-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Alex Crichton Copyright (c) 2017 The Tokio Authors  |
| [futures-sink](https://github.com/rust-lang/futures-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Alex Crichton Copyright (c) 2017 The Tokio Authors  |
| [futures-task](https://github.com/rust-lang/futures-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Alex Crichton Copyright (c) 2017 The Tokio Authors  |
| [futures-util](https://github.com/rust-lang/futures-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Alex Crichton Copyright (c) 2017 The Tokio Authors  |
| [fxhash](https://github.com/cbreeden/fxhash) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) cbreeden <<EMAIL>> |
| [gc-arena](https://github.com/kyren/gc-arena) | [MIT](#MIT) | Copyright (c) kyren <<EMAIL>> |
| [gc-arena-derive](https://github.com/kyren/gc-arena) | [MIT](#MIT) | Copyright (c) kyren <<EMAIL>> |
| [getrandom](https://github.com/rust-random/getrandom) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright 2018 Developers of the Rand project Copyright (c) 2014 The Rust Project Developers  |
| [gfx-auxil](https://github.com/gfx-rs/gfx) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Gfx-rs Developers |
| [gfx-backend-dx11](https://github.com/gfx-rs/gfx) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Gfx-rs Developers |
| [gfx-backend-dx12](https://github.com/gfx-rs/gfx) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Gfx-rs Developers |
| gfx-backend-empty | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Gfx-rs Developers |
| [gfx-backend-gl](https://github.com/gfx-rs/gfx) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Gfx-rs Developers |
| [gfx-backend-metal](https://github.com/gfx-rs/gfx) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Gfx-rs Developers |
| [gfx-backend-vulkan](https://github.com/gfx-rs/gfx) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Gfx-rs Developers |
| [gfx-hal](https://github.com/gfx-rs/gfx) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Gfx-rs Developers |
| [gif](https://github.com/image-rs/image-gif) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 nwin  |
| [gimli](https://github.com/gimli-rs/gimli) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Rust Project Developers  |
| [glob](https://github.com/rust-lang/glob) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [glow](https://github.com/grovesNL/glow) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Joshua Groves <<EMAIL>>, Dzmitry Malyshau <<EMAIL>> |
| [gpu-alloc](https://github.com/zakarumych/gpu-alloc) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Zakarum <<EMAIL>> |
| [gpu-alloc-types](https://github.com/zakarumych/gpu-alloc) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Zakarum <<EMAIL>> |
| [gpu-descriptor](https://github.com/zakarumych/gpu-descriptor) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Zakarum <<EMAIL>> |
| [gpu-descriptor-types](https://github.com/zakarumych/gpu-descriptor) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Zakarum <<EMAIL>> |
| [hashbrown](https://github.com/rust-lang/hashbrown) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Amanieu d'Antras  |
| [heck](https://github.com/withoutboats/heck) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Rust Project Developers  |
| [hermit-abi](https://github.com/hermitcore/libhermit-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Stefan Lankes |
| [http](https://github.com/hyperium/http) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 http-rs authors  |
| [humantime](https://github.com/tailhook/humantime) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 The humantime Developers Copyright (c) 2016 Pyfisch Copyright © 2005-2013 Rich Felker  |
| [ident_case](https://github.com/TedDriggs/ident_case) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Ted Driggs <<EMAIL>> |
| [idna](https://github.com/servo/rust-url/) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2013-2016 The rust-url developers  |
| [image](https://github.com/image-rs/image) | [MIT](#MIT) | Copyright (c) 2014 PistonDevelopers  |
| [indexmap](https://github.com/bluss/indexmap) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016--2017  |
| [indicatif](https://github.com/mitsuhiko/indicatif) | [MIT](#MIT) | Copyright (c) 2017 Armin Ronacher <<EMAIL>>  |
| [inplace_it](https://github.com/NotIntMan/inplace_it) | [MIT](#MIT) | Copyright (c) 2018 Dmitry Demin <<EMAIL>>  |
| [iovec](https://github.com/carllerche/iovec) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Carl Lerche  |
| [isahc](https://github.com/sagebind/isahc) | [MIT](#MIT) | Copyright (c) 2019 Stephen M. Coakley  |
| [itoa](https://github.com/dtolnay/itoa) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) David Tolnay <<EMAIL>> |
| [jni](https://github.com/jni-rs/jni-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Prevoty, Inc. and jni-rs contributors  |
| [jni-sys](https://github.com/sfackler/rust-jni-sys) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The rust-jni-sys Developers  |
| [jobserver](https://github.com/alexcrichton/jobserver-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [jpeg-decoder](https://github.com/image-rs/jpeg-decoder) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 The jpeg-decoder Developers  |
| [js-sys](https://github.com/rustwasm/wasm-bindgen/tree/master/crates/js-sys) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [json](https://github.com/maciejhirsz/json-rust) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Maciej Hirsz <<EMAIL>>  |
| [kernel32-sys](https://github.com/retep998/winapi-rs) | [MIT](#MIT) | Copyright (c) 2015-2018 The winapi-rs Developers  |
| [khronos-egl](https://github.com/timothee-haudebourg/khronos-egl) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Timothée Haudebourg <<EMAIL>>, Sean Kerr <<EMAIL>> |
| [lazy_static](https://github.com/rust-lang-nursery/lazy-static.rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2010 The Rust Project Developers  |
| [lazycell](https://github.com/indiv0/lazycell) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Alex Crichton <<EMAIL>>, Nikita Pekin <<EMAIL>> |
| [libc](https://github.com/rust-lang/libc) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014-2020 The Rust Project Developers  |
| [libloading](https://github.com/nagisa/rust_libloading/) | [ISC](#ISC) | Copyright © 2015, Simonas Kazlauskas  |
| [libnghttp2-sys](https://github.com/alexcrichton/nghttp2-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [libz-sys](https://github.com/rust-lang/libz-sys) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton Copyright (c) 2020 Josh Triplett  |
| [lock_api](https://github.com/Amanieu/parking_lot) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 The Rust Project Developers  |
| [log](https://github.com/rust-lang/log) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [lyon](https://github.com/nical/lyon) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2013 Nicolas Silva  |
| [lyon_algorithms](https://github.com/nical/lyon) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2013 Nicolas Silva  |
| [lyon_geom](https://github.com/nical/lyon) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2013 Nicolas Silva  |
| [lyon_path](https://github.com/nical/lyon) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2013 Nicolas Silva  |
| [lyon_tessellation](https://github.com/nical/lyon) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2013 Nicolas Silva  |
| [lzma-rs](https://github.com/gendx/lzma-rs) | [MIT](#MIT) | Copyright (c) 2017 - 2018  Guillaume Endignoux  |
| [mach](https://github.com/fitzgen/mach) | [BSD-2-Clause](#BSD-2-Clause) | Copyright (c) 2015, Nick Fitzgerald  |
| [malloc_buf](https://github.com/SSheldon/malloc_buf) | [MIT](#MIT) | Copyright (c) 2020 Steven Sheldon  |
| [matches](https://github.com/SimonSapin/rust-std-candidates) | [MIT](#MIT) | Copyright (c) 2014-2016 Simon Sapin  |
| [maybe-uninit](https://github.com/est31/maybe-uninit) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2010 The Rust Project Developers  |
| [memchr](https://github.com/BurntSushi/rust-memchr) | [MIT](#MIT)/[Unlicense](#Unlicense) | Copyright (c) 2015 Andrew Gallant  |
| [memmap2](https://github.com/RazrFalcon/memmap2-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2020 Evgeniy Reizner Copyright (c) 2015 Dan Burkert  |
| [memoffset](https://github.com/Gilnaa/memoffset) | [MIT](#MIT) | Copyright (c) 2017 Gilad Naaman  |
| [metal](https://github.com/gfx-rs/metal-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2010 The Rust Project Developers  |
| [mime](https://github.com/hyperium/mime) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Sean McArthur  |
| [miniz_oxide](https://github.com/Frommi/miniz_oxide/tree/master/miniz_oxide) | [Apache-2.0](#Apache-20)/[MIT](#MIT)/[Zlib](#Zlib) | Copyright (c) 2017 Frommi  |
| [mio](https://github.com/tokio-rs/mio) | [MIT](#MIT) | Copyright (c) 2014 Carl Lerche and other MIO contributors  |
| [mio-extras](https://github.com/dimbleby/mio-extras) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Mio authors  |
| [miow](https://github.com/alexcrichton/miow) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [naga](https://github.com/gfx-rs/naga) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Dzmitry Malyshau <<EMAIL>> |
| [ndk](https://github.com/rust-windowing/android-ndk-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Rust Windowing contributors |
| [ndk-glue](https://github.com/rust-windowing/android-ndk-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Rust Windowing contributors |
| [ndk-macro](https://github.com/rust-windowing/android-ndk-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Rust Windowing contributors |
| [ndk-sys](https://github.com/rust-windowing/android-ndk-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Rust Windowing contributors |
| nellymoser-rs | [MIT](#MIT) | Copyright (c) 2021 relrelb Copyright (c) 2007 a840bda5870ba11f19698ff6eb9581dfb0f95fa5, 539459aeb7d425140b62a3ec7dbf6dc8e408a306, and 520e17cd55896441042b14df2566a6eb610ed444 Copyright (c) 2007 Loic Minier, Benjamin Larsson. Derived from MIT licensed source in the nelly2pcm and FFmpeg projects. |
| [net2](https://github.com/deprecrated/net2-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [nihav-vp6](https://github.com/ruffle-rs/nihav-vp6) | [MIT](#MIT) | Copyright (c) 2021 Kostya Shishkov  |
| [nix](https://github.com/nix-rust/nix) | [MIT](#MIT) | Copyright (c) 2015 Carl Lerche + nix-rust Authors  |
| [nom](https://github.com/Geal/nom) | [MIT](#MIT) | Copyright (c) 2014-2019 Geoffroy Couprie  |
| [num-complex](https://github.com/rust-num/num-complex) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [num-derive](https://github.com/rust-num/num-derive) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [num-integer](https://github.com/rust-num/num-integer) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [num-iter](https://github.com/rust-num/num-iter) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [num-rational](https://github.com/rust-num/num-rational) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [num-traits](https://github.com/rust-num/num-traits) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [num_cpus](https://github.com/seanmonstar/num_cpus) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015  |
| [num_enum](https://github.com/illicitonion/num_enum) | [BSD-3-Clause](#BSD-3-Clause) | Copyright (c) 2018, Daniel Wagner-Hall  |
| [num_enum_derive](https://github.com/illicitonion/num_enum) | [BSD-3-Clause](#BSD-3-Clause) | Copyright (c) 2018, Daniel Wagner-Hall  |
| [num_enum_derive](https://github.com/illicitonion/num_enum) | [BSD-3-Clause](#BSD-3-Clause) | Copyright (c) 2018, Daniel Wagner-Hall  |
| [number_prefix](https://github.com/ogham/rust-number-prefix) | [MIT](#MIT) | Copyright (c) Benjamin Sago <<EMAIL>> |
| [objc](http://github.com/SSheldon/rust-objc) | [MIT](#MIT) | Copyright (c) Steven Sheldon  |
| [objc-foundation](http://github.com/SSheldon/rust-objc-foundation) | [MIT](#MIT) | Copyright (c) Steven Sheldon |
| [objc_exception](http://github.com/SSheldon/rust-objc-exception) | [MIT](#MIT) | Copyright (c) Steven Sheldon |
| [objc_id](http://github.com/SSheldon/rust-objc-id) | [MIT](#MIT) | Copyright (c) Steven Sheldon |
| [object](https://github.com/gimli-rs/object) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Gimli Developers  |
| [oboe](https://github.com/katyo/oboe-rs) | [Apache-2.0](#Apache-20) | Copyright (c) K. <<EMAIL>> |
| [oboe-sys](https://github.com/katyo/oboe-rs) | [Apache-2.0](#Apache-20) | Copyright (c) K. <<EMAIL>> |
| [once_cell](https://github.com/matklad/once_cell) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Aleksey Kladov <<EMAIL>> |
| [openssl-probe](https://github.com/alexcrichton/openssl-probe) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [openssl-sys](https://github.com/sfackler/rust-openssl) | [MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [os_str_bytes](https://github.com/dylni/os_str_bytes) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 Dylan Iuzzolino  |
| [output_vt100](https://github.com/Phundrak/output-vt100-rs) | [MIT](#MIT) | Copyright (c) 2016 rust-derive-builder contributors  |
| [owned_ttf_parser](https://github.com/alexheretic/owned-ttf-parser) | [Apache-2.0](#Apache-20) | Copyright 2020 Alex Butler  |
| [parking](https://github.com/stjepang/parking) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Stjepan Glavina <<EMAIL>>, The Rust Project Developers |
| [parking_lot](https://github.com/Amanieu/parking_lot) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 The Rust Project Developers  |
| [parking_lot_core](https://github.com/Amanieu/parking_lot) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 The Rust Project Developers  |
| [path-slash](https://github.com/rhysd/path-slash) | [MIT](#MIT) | Copyright (c) 2018 rhysd  |
| [peeking_take_while](https://github.com/fitzgen/peeking_take_while) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Rust Project Developers  |
| [percent-encoding](https://github.com/servo/rust-url/) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2013-2016 The rust-url developers  |
| [petgraph](https://github.com/petgraph/petgraph) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015  |
| [pin-project](https://github.com/taiki-e/pin-project) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Taiki Endo <<EMAIL>> |
| [pin-project-internal](https://github.com/taiki-e/pin-project) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Taiki Endo <<EMAIL>> |
| [pin-project-lite](https://github.com/taiki-e/pin-project-lite) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Taiki Endo <<EMAIL>> |
| [pin-utils](https://github.com/rust-lang-nursery/pin-utils) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018 The pin-utils authors  |
| [pkg-config](https://github.com/rust-lang/pkg-config-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [png](https://github.com/image-rs/image-png.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 nwin  |
| [ppv-lite86](https://github.com/cryptocorrosion/cryptocorrosion) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 The CryptoCorrosion Contributors  |
| [pretty_assertions](https://github.com/colin-kiegel/rust-pretty-assertions) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 rust-derive-builder contributors  |
| [primal-check](https://github.com/huonw/primal) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Huon Wilson  |
| [proc-macro-crate](https://github.com/bkchr/proc-macro-crate) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Bastian Köcher <***********> |
| [proc-macro-error](https://gitlab.com/CreepySkeleton/proc-macro-error) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019-2020 CreepySkeleton  |
| [proc-macro-error-attr](https://gitlab.com/CreepySkeleton/proc-macro-error) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019-2020 CreepySkeleton  |
| [proc-macro-hack](https://github.com/dtolnay/proc-macro-hack) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018 David Tolnay  |
| [proc-macro-nested](https://github.com/dtolnay/proc-macro-hack) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018 David Tolnay  |
| [proc-macro2](https://github.com/alexcrichton/proc-macro2) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [publicsuffix](https://github.com/rushmorem/publicsuffix) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Rushmore Mushambi  |
| [qstring](https://github.com/algesten/qstring) | [MIT](#MIT) | Copyright (c) Martin Algesten <<EMAIL>> |
| [quick-xml](https://github.com/tafia/quick-xml) | [MIT](#MIT) | Copyright (c) 2016 Johann Tuffe  |
| [quote](https://github.com/dtolnay/quote) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 The Rust Project Developers  |
| [rand](https://github.com/rust-random/rand) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright 2018 Developers of the Rand project Copyright (c) 2014 The Rust Project Developers  |
| [rand_chacha](https://github.com/rust-random/rand) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright 2018 Developers of the Rand project Copyright (c) 2014 The Rust Project Developers  |
| [rand_core](https://github.com/rust-random/rand) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright 2018 Developers of the Rand project Copyright (c) 2014 The Rust Project Developers  |
| [range-alloc](https://github.com/gfx-rs/gfx) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Gfx-rs Developers |
| [raw-window-handle](https://github.com/rust-windowing/raw-window-handle) | [MIT](#MIT) | Copyright (c) 2019 Osspial  |
| [rayon](https://github.com/rayon-rs/rayon) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2010 The Rust Project Developers  |
| [rayon-core](https://github.com/rayon-rs/rayon) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2010 The Rust Project Developers  |
| [redox_syscall](https://gitlab.redox-os.org/redox-os/syscall) | [MIT](#MIT) | Copyright (c) 2017 Redox OS Developers  |
| [redox_syscall](https://gitlab.redox-os.org/redox-os/syscall) | [MIT](#MIT) | Copyright (c) 2017 Redox OS Developers  |
| [redox_users](https://gitlab.redox-os.org/redox-os/users) | [MIT](#MIT) | Copyright (c) 2017 Jose Narvaez  |
| [regex](https://github.com/rust-lang/regex) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [regex-automata](https://github.com/BurntSushi/regex-automata) | [MIT](#MIT)/[Unlicense](#Unlicense) | Copyright (c) 2015 Andrew Gallant  |
| [regex-syntax](https://github.com/rust-lang/regex) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [rfd](https://github.com/PolyMeilex/rfd) | [MIT](#MIT) | Copyright (c) 2022 Bartłomiej Maryńczak  |
| [ring](https://github.com/briansmith/ring) |  | Copyright 2015-2016 Brian Smith. |
| [rle-decode-fast](https://github.com/WanzenBug/rle-decode-helper) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright 2019 Moritz "WanzenBug" Wanzenböck  |
| [rust-argon2](https://github.com/sru-systems/rust-argon2) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Martijn Rijkeboer <<EMAIL>>  |
| [rustc-demangle](https://github.com/alexcrichton/rustc-demangle) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [rustc-hash](https://github.com/rust-lang-nursery/rustc-hash) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Rust Project Developers |
| [rustc_version](https://github.com/Kimundi/rustc-version-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 The Rust Project Developers  |
| [rustdct](https://github.com/ejmahler/rust_dct) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The RustFFT Developers  |
| [rustfft](https://github.com/ejmahler/RustFFT) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The RustFFT Developers  |
| [rustls](https://github.com/ctz/rustls) | [Apache-2.0](#Apache-20)/[ISC](#ISC)/[MIT](#MIT) | Copyright (c) 2016 Joseph Birr-Pixton <<EMAIL>>  |
| [rusttype](https://gitlab.redox-os.org/redox-os/rusttype) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Dylan Ede  |
| [ryu](https://github.com/dtolnay/ryu) | [Apache-2.0](#Apache-20)/BSL-1.0 | Copyright (c) David Tolnay <<EMAIL>> |
| [same-file](https://github.com/BurntSushi/same-file) | [MIT](#MIT)/[Unlicense](#Unlicense) | Copyright (c) 2017 Andrew Gallant  |
| [schannel](https://github.com/steffengy/schannel-rs) | [MIT](#MIT) | Copyright (c) 2015 steffengy  |
| [scoped-tls](https://github.com/alexcrichton/scoped-tls) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [scoped_threadpool](https://github.com/Kimundi/scoped-threadpool-rs) | [MIT](#MIT) | Copyright (c) 2015 Marvin Löbel  |
| [scopeguard](https://github.com/bluss/scopeguard) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016-2019 Ulrik Sverdrup "bluss" and scopeguard developers  |
| [sct](https://github.com/ctz/sct.rs) | [Apache-2.0](#Apache-20)/[ISC](#ISC)/[MIT](#MIT) | Copyright (c) 2016 Joseph Birr-Pixton <<EMAIL>>  |
| [semver](https://github.com/steveklabnik/semver) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [semver-parser](https://github.com/steveklabnik/semver-parser) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Steve Klabnik  |
| [serde](https://github.com/serde-rs/serde) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Erick Tryzelaar <<EMAIL>>, David Tolnay <<EMAIL>> |
| [serde_derive](https://github.com/serde-rs/serde) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Erick Tryzelaar <<EMAIL>>, David Tolnay <<EMAIL>> |
| [serde_json](https://github.com/serde-rs/json) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Erick Tryzelaar <<EMAIL>>, David Tolnay <<EMAIL>> |
| [sha1](https://github.com/mitsuhiko/rust-sha1) | [BSD-3-Clause](#BSD-3-Clause) | Copyright (c) 2014 by Armin Ronacher. Copyright (c) 2013 Koka El Kiwi  |
| [shlex](https://github.com/comex/rust-shlex) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 Nicholas Allegra (comex).  |
| [sid](https://github.com/nical/sid) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Nicolas Silva <<EMAIL>> |
| [slab](https://github.com/carllerche/slab) | [MIT](#MIT) | Copyright (c) 2019 Carl Lerche  |
| [slice-deque](https://github.com/gnzlbg/slice_deque) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Gonzalo Brito Gadeschi Copyright (c) 2017 The Rust Project Developers  |
| [slotmap](https://github.com/orlp/slotmap) | [Zlib](#Zlib) | Copyright (c) 2021 Orson Peters <<EMAIL>>  |
| [sluice](https://github.com/sagebind/sluice) | [MIT](#MIT) | Copyright (c) 2017 Stephen M. Coakley  |
| [smallvec](https://github.com/servo/rust-smallvec) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018 The Servo Project Developers  |
| [smithay-client-toolkit](https://github.com/smithay/client-toolkit) | [MIT](#MIT) | Copyright (c) 2018 Victor Berger  |
| [socket2](https://github.com/alexcrichton/socket2-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [spin](https://github.com/mvdnes/spin-rs.git) | [MIT](#MIT) | Copyright (c) 2014 Mathijs van de Nes  |
| [spinning_top](https://github.com/rust-osdev/spinning_top) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2020 Philipp Oppermann  |
| [spirv_cross](https://github.com/grovesNL/spirv_cross) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Joshua Groves <<EMAIL>> |
| [spirv_headers](https://github.com/gfx-rs/rspirv) | [Apache-2.0](#Apache-20) | Copyright (c) Lei Zhang <<EMAIL>> |
| [standback](https://github.com/jhpratt/standback) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2020 Jacob Pratt  |
| [stdweb](https://github.com/koute/stdweb) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Jan Bujak  |
| [stdweb-derive](https://github.com/koute/stdweb) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Jan Bujak  |
| [stdweb-internal-macros](https://github.com/koute/stdweb) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Jan Bujak  |
| [stdweb-internal-runtime](https://github.com/koute/stdweb) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Jan Bujak  |
| [storage-map](https://github.com/kvark/storage-map) | [Apache-2.0](#Apache-20) | Copyright (c) Dzmitry Malyshau <<EMAIL>> |
| [strength_reduce](http://github.com/ejmahler/strength_reduce) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Elliott Mahler <<EMAIL>> |
| [strsim](https://github.com/dguo/strsim-rs) | [MIT](#MIT) | Copyright (c) 2015 Danny Guo Copyright (c) 2016 Titus Wormer <<EMAIL>>  |
| [strsim](https://github.com/dguo/strsim-rs) | [MIT](#MIT) | Copyright (c) 2015 Danny Guo Copyright (c) 2016 Titus Wormer <<EMAIL>> Copyright (c) 2018 Akash Kurdekar  |
| [svg](https://github.com/bodoni/svg) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright 2015–2021 The svg Developers Copyright 2015–2021 The svg Developers  |
| [swf](https://github.com/ruffle-rs/ruffle) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018 Ruffle LLC <<EMAIL>> and Ruffle contributors |
| [symphonia](https://github.com/pdeljanov/Symphonia) | [MPL-2.0](#MPL-20) | Copyright (c) Philip Deljanov |
| [syn](https://github.com/dtolnay/syn) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) David Tolnay <<EMAIL>> |
| [synstructure](https://github.com/mystor/synstructure) | [MIT](#MIT) | Copyright 2016 Nika Layzell  |
| [tar](https://github.com/alexcrichton/tar-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [termcolor](https://github.com/BurntSushi/termcolor) | [MIT](#MIT)/[Unlicense](#Unlicense) | Copyright (c) 2015 Andrew Gallant  |
| [terminal_size](https://github.com/eminence/terminal-size) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The terminal-size Developers  |
| [textwrap](https://github.com/mgeisler/textwrap) | [MIT](#MIT) | Copyright (c) 2016 Martin Geisler  |
| [thiserror](https://github.com/dtolnay/thiserror) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) David Tolnay <<EMAIL>> |
| [thiserror-impl](https://github.com/dtolnay/thiserror) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) David Tolnay <<EMAIL>> |
| [thread_local](https://github.com/Amanieu/thread_local-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 The Rust Project Developers  |
| [thunderdome](https://github.com/LPGhatguy/thunderdome) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2020 Lucien Greathouse  |
| [tiff](https://github.com/image-rs/image-tiff) | [MIT](#MIT) | Copyright (c) 2018 PistonDevelopers  |
| [time](https://github.com/time-rs/time) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 Jacob Pratt  |
| [time-macros](https://github.com/time-rs/time) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 Jacob Pratt  |
| [time-macros-impl](https://github.com/time-rs/time) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 Jacob Pratt  |
| [tinyvec](https://github.com/Lokathor/tinyvec) | [Apache-2.0](#Apache-20)/[MIT](#MIT)/[Zlib](#Zlib) | Copyright (c) Lokathor <<EMAIL>> |
| [tinyvec_macros](https://github.com/Soveu/tinyvec_macros) | [Apache-2.0](#Apache-20)/[MIT](#MIT)/[Zlib](#Zlib) | Copyright (c) 2020 Soveu  |
| [toml](https://github.com/alexcrichton/toml-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [tracing](https://github.com/tokio-rs/tracing) | [MIT](#MIT) | Copyright (c) 2019 Tokio Contributors  |
| [tracing-attributes](https://github.com/tokio-rs/tracing) | [MIT](#MIT) | Copyright (c) 2019 Tokio Contributors  |
| [tracing-core](https://github.com/tokio-rs/tracing) | [MIT](#MIT) | Copyright (c) 2019 Tokio Contributors  |
| [tracing-futures](https://github.com/tokio-rs/tracing) | [MIT](#MIT) | Copyright (c) 2019 Tokio Contributors  |
| [transpose](http://github.com/ejmahler/transpose) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Elliott Mahler <<EMAIL>> |
| [ttf-parser](https://github.com/RazrFalcon/ttf-parser) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2018 Evgeniy Reizner  |
| [typed-arena](https://github.com/SimonSapin/rust-typed-arena) | [MIT](#MIT) | Copyright (c) 2018 The typed-arena developers  |
| [unicode-bidi](https://github.com/servo/unicode-bidi) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Rust Project Developers  |
| [unicode-normalization](https://github.com/unicode-rs/unicode-normalization) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Rust Project Developers  |
| [unicode-segmentation](https://github.com/unicode-rs/unicode-segmentation) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Rust Project Developers  |
| [unicode-width](https://github.com/unicode-rs/unicode-width) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Rust Project Developers  |
| [unicode-xid](https://github.com/unicode-rs/unicode-xid) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Rust Project Developers  |
| [unreachable](https://github.com/reem/rust-unreachable.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The rust-unreachable Developers  |
| [untrusted](https://github.com/briansmith/untrusted) | [ISC](#ISC) | Copyright (c) Brian Smith <<EMAIL>> |
| [ureq](https://github.com/algesten/ureq) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2019 Martin Algesten  |
| [url](https://github.com/servo/rust-url) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2013-2016 The rust-url developers  |
| [vcpkg](https://github.com/mcgoo/vcpkg-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017 Jim McGrath  |
| [vec_map](https://github.com/contain-rs/vec-map) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The Rust Project Developers  |
| [version_check](https://github.com/SergioBenitez/version_check) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2017-2018 Sergio Benitez  |
| [void](https://github.com/reem/rust-void.git) | [MIT](#MIT) | Copyright (c) 2015 The rust-void Developers  |
| [vswhom](https://github.com/nabijaczleweli/vswhom.rs) | [MIT](#MIT) | Copyright (c) 2019 nabijaczleweli  |
| [vswhom-sys](https://github.com/nabijaczleweli/vswhom-sys.rs) | [MIT](#MIT) | Copyright (c) 2019 nabijaczleweli  |
| [waker-fn](https://github.com/stjepang/waker-fn) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) Stjepan Glavina <<EMAIL>> |
| [walkdir](https://github.com/BurntSushi/walkdir) | [MIT](#MIT)/[Unlicense](#Unlicense) | Copyright (c) 2015 Andrew Gallant  |
| [wasi](https://github.com/bytecodealliance/wasi) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) The Cranelift Project Developers |
| [wasm-bindgen](https://github.com/rustwasm/wasm-bindgen) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [wasm-bindgen-backend](https://github.com/rustwasm/wasm-bindgen/tree/master/crates/backend) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [wasm-bindgen-futures](https://github.com/rustwasm/wasm-bindgen/tree/master/crates/futures) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [wasm-bindgen-macro](https://github.com/rustwasm/wasm-bindgen/tree/master/crates/macro) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [wasm-bindgen-macro-support](https://github.com/rustwasm/wasm-bindgen/tree/master/crates/macro-support) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [wasm-bindgen-shared](https://github.com/rustwasm/wasm-bindgen/tree/master/crates/shared) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [wasm-bindgen-test](https://github.com/rustwasm/wasm-bindgen) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [wasm-bindgen-test-macro](https://github.com/rustwasm/wasm-bindgen) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [wayland-client](https://github.com/smithay/wayland-rs) | [MIT](#MIT) | Copyright (c) 2015 Victor Berger  |
| [wayland-commons](https://github.com/smithay/wayland-rs) | [MIT](#MIT) | Copyright (c) 2015 Victor Berger  |
| [wayland-cursor](https://github.com/smithay/wayland-rs) | [MIT](#MIT) | Copyright (c) 2015 Victor Berger  |
| [wayland-protocols](https://github.com/smithay/wayland-rs) | [MIT](#MIT) | Copyright (c) 2015 Victor Berger  |
| [wayland-scanner](https://github.com/smithay/wayland-rs) | [MIT](#MIT) | Copyright (c) 2015 Victor Berger  |
| [wayland-sys](https://github.com/smithay/wayland-rs) | [MIT](#MIT) | Copyright (c) 2015 Victor Berger  |
| [weak-table](https://github.com/tov/weak-table-rs) | [MIT](#MIT) | Copyright (c) 2018 Jesse A. Tov  |
| [web-sys](https://github.com/rustwasm/wasm-bindgen/tree/master/crates/web-sys) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 Alex Crichton  |
| [web-time](https://github.com/daxpedda/web-time) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2023 dAxpeDDa <<EMAIL>>  |
| [webbrowser](https://github.com/amodm/webbrowser-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 Amod Malviya  |
| [webpki](https://github.com/briansmith/webpki) | Custom ISC-style | Copyright 2015 Brian Smith.  |
| [webpki-roots](https://github.com/ctz/webpki-roots) | [MPL-2.0](#MPL-20) | Copyright (c) Joseph Birr-Pixton <<EMAIL>> |
| [weezl](https://github.com/image-rs/lzw.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) HeroicKatora 2020  |
| [wgpu](https://github.com/gfx-rs/wgpu-rs) | [MPL-2.0](#MPL-20) | Copyright (c) wgpu developers |
| [wgpu-core](https://github.com/gfx-rs/wgpu) | [MPL-2.0](#MPL-20) | Copyright (c) wgpu developers |
| [wgpu-types](https://github.com/gfx-rs/wgpu) | [MPL-2.0](#MPL-20) | Copyright (c) wgpu developers |
| [widestring](https://github.com/starkat99/widestring-rs.git) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2016 Kathryn Long  |
| [winapi](https://github.com/retep998/winapi-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015-2018 The winapi-rs Developers  |
| [winapi-build](https://github.com/retep998/winapi-rs) | [MIT](#MIT) | Copyright (c) 2015-2018 The winapi-rs Developers  |
| [winapi-i686-pc-windows-gnu](https://github.com/retep998/winapi-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015-2018 The winapi-rs Developers  |
| [winapi-util](https://github.com/BurntSushi/winapi-util) | [MIT](#MIT)/[Unlicense](#Unlicense) | Copyright (c) 2017 Andrew Gallant  |
| [winapi-x86_64-pc-windows-gnu](https://github.com/retep998/winapi-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015-2018 The winapi-rs Developers  |
| [winit](https://github.com/rust-windowing/winit) | [Apache-2.0](#Apache-20) | Copyright (c) The winit contributors, Pierre Krieger <<EMAIL>> |
| [winreg](https://github.com/gentoo90/winreg-rs) | [MIT](#MIT) | Copyright (c) 2015 Igor Shaula  |
| [wio](https://github.com/retep998/wio-rs) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 The winapi-rs Developers  |
| [ws2_32-sys](https://github.com/retep998/winapi-rs) | [MIT](#MIT) | Copyright (c) 2015-2018 The winapi-rs Developers  |
| [x11-clipboard](https://github.com/quininer/x11-clipboard) | [MIT](#MIT) | Copyright (c) 2017 <EMAIL>  |
| [x11-dl](https://github.com/erlepereira/x11-rs.git) | [MIT](#MIT) | Copyright (c) daggerbot <<EMAIL>>, Erle Pereira <<EMAIL>> |
| [xattr](https://github.com/Stebalien/xattr) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2015 Steven Allen  |
| [xcb](https://github.com/rtbo/rust-xcb) | [MIT](#MIT) | Copyright (c) 2013 James Miller <<EMAIL>> Copyright (c) 2016  |
| [xcursor](https://github.com/esposm03/xcursor-rs) | [MIT](#MIT) | Copyright (c) 2020 Samuele Esposito  |
| [xdg](https://github.com/whitequark/rust-xdg) | [Apache-2.0](#Apache-20)/[MIT](#MIT) | Copyright (c) 2014 The Rust Project Developers  |
| [xml-rs](https://github.com/netvl/xml-rs) | [MIT](#MIT) | Copyright (c) 2014 Vladimir Matveev  |

# Licenses of Third-Party Libraries

## 0BSD

```
Permission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
```

## Apache-2.0

```

                                 Apache License
                           Version 2.0, January 2004
                        http://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS
```

## BSD-2-Clause

```
Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
```

## BSD-3-Clause

```
Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

3. Neither the name of the copyright holder nor the names of its contributors may be used to endorse or promote products derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
```

## CC0-1.0

```
Creative Commons Legal Code

CC0 1.0 Universal

    CREATIVE COMMONS CORPORATION IS NOT A LAW FIRM AND DOES NOT PROVIDE
    LEGAL SERVICES. DISTRIBUTION OF THIS DOCUMENT DOES NOT CREATE AN
    ATTORNEY-CLIENT RELATIONSHIP. CREATIVE COMMONS PROVIDES THIS
    INFORMATION ON AN "AS-IS" BASIS. CREATIVE COMMONS MAKES NO WARRANTIES
    REGARDING THE USE OF THIS DOCUMENT OR THE INFORMATION OR WORKS
    PROVIDED HEREUNDER, AND DISCLAIMS LIABILITY FOR DAMAGES RESULTING FROM
    THE USE OF THIS DOCUMENT OR THE INFORMATION OR WORKS PROVIDED
    HEREUNDER.

Statement of Purpose

The laws of most jurisdictions throughout the world automatically confer
exclusive Copyright and Related Rights (defined below) upon the creator
and subsequent owner(s) (each and all, an "owner") of an original work of
authorship and/or a database (each, a "Work").

Certain owners wish to permanently relinquish those rights to a Work for
the purpose of contributing to a commons of creative, cultural and
scientific works ("Commons") that the public can reliably and without fear
of later claims of infringement build upon, modify, incorporate in other
works, reuse and redistribute as freely as possible in any form whatsoever
and for any purposes, including without limitation commercial purposes.
These owners may contribute to the Commons to promote the ideal of a free
culture and the further production of creative, cultural and scientific
works, or to gain reputation or greater distribution for their Work in
part through the use and efforts of others.

For these and/or other purposes and motivations, and without any
expectation of additional consideration or compensation, the person
associating CC0 with a Work (the "Affirmer"), to the extent that he or she
is an owner of Copyright and Related Rights in the Work, voluntarily
elects to apply CC0 to the Work and publicly distribute the Work under its
terms, with knowledge of his or her Copyright and Related Rights in the
Work and the meaning and intended legal effect of CC0 on those rights.

1. Copyright and Related Rights. A Work made available under CC0 may be
protected by copyright and related or neighboring rights ("Copyright and
Related Rights"). Copyright and Related Rights include, but are not
limited to, the following:

  i. the right to reproduce, adapt, distribute, perform, display,
     communicate, and translate a Work;
 ii. moral rights retained by the original author(s) and/or performer(s);
iii. publicity and privacy rights pertaining to a person's image or
     likeness depicted in a Work;
 iv. rights protecting against unfair competition in regards to a Work,
     subject to the limitations in paragraph 4(a), below;
  v. rights protecting the extraction, dissemination, use and reuse of data
     in a Work;
 vi. database rights (such as those arising under Directive 96/9/EC of the
     European Parliament and of the Council of 11 March 1996 on the legal
     protection of databases, and under any national implementation
     thereof, including any amended or successor version of such
     directive); and
vii. other similar, equivalent or corresponding rights throughout the
     world based on applicable law or treaty, and any national
     implementations thereof.

2. Waiver. To the greatest extent permitted by, but not in contravention
of, applicable law, Affirmer hereby overtly, fully, permanently,
irrevocably and unconditionally waives, abandons, and surrenders all of
Affirmer's Copyright and Related Rights and associated claims and causes
of action, whether now known or unknown (including existing as well as
future claims and causes of action), in the Work (i) in all territories
worldwide, (ii) for the maximum duration provided by applicable law or
treaty (including future time extensions), (iii) in any current or future
medium and for any number of copies, and (iv) for any purpose whatsoever,
including without limitation commercial, advertising or promotional
purposes (the "Waiver"). Affirmer makes the Waiver for the benefit of each
member of the public at large and to the detriment of Affirmer's heirs and
successors, fully intending that such Waiver shall not be subject to
revocation, rescission, cancellation, termination, or any other legal or
equitable action to disrupt the quiet enjoyment of the Work by the public
as contemplated by Affirmer's express Statement of Purpose.

3. Public License Fallback. Should any part of the Waiver for any reason
be judged legally invalid or ineffective under applicable law, then the
Waiver shall be preserved to the maximum extent permitted taking into
account Affirmer's express Statement of Purpose. In addition, to the
extent the Waiver is so judged Affirmer hereby grants to each affected
person a royalty-free, non transferable, non sublicensable, non exclusive,
irrevocable and unconditional license to exercise Affirmer's Copyright and
Related Rights in the Work (i) in all territories worldwide, (ii) for the
maximum duration provided by applicable law or treaty (including future
time extensions), (iii) in any current or future medium and for any number
of copies, and (iv) for any purpose whatsoever, including without
limitation commercial, advertising or promotional purposes (the
"License"). The License shall be deemed effective as of the date CC0 was
applied by Affirmer to the Work. Should any part of the License for any
reason be judged legally invalid or ineffective under applicable law, such
partial invalidity or ineffectiveness shall not invalidate the remainder
of the License, and in such case Affirmer hereby affirms that he or she
will not (i) exercise any of his or her remaining Copyright and Related
Rights in the Work or (ii) assert any associated claims and causes of
action with respect to the Work, in either case contrary to Affirmer's
express Statement of Purpose.

4. Limitations and Disclaimers.

 a. No trademark or patent rights held by Affirmer are waived, abandoned,
    surrendered, licensed or otherwise affected by this document.
 b. Affirmer offers the Work as-is and makes no representations or
    warranties of any kind concerning the Work, express, implied,
    statutory or otherwise, including without limitation warranties of
    title, merchantability, fitness for a particular purpose, non
    infringement, or the absence of latent or other defects, accuracy, or
    the present or absence of errors, whether or not discoverable, all to
    the greatest extent permissible under applicable law.
 c. Affirmer disclaims responsibility for clearing rights of other persons
    that may apply to the Work or any use thereof, including without
    limitation any person's Copyright and Related Rights in the Work.
    Further, Affirmer disclaims responsibility for obtaining any necessary
    consents, permissions or other rights required for any use of the
    Work.
 d. Affirmer understands and acknowledges that Creative Commons is not a
    party to this document and has no duty or obligation with respect to
    this CC0 or use of the Work.
```

## ISC

```
Copyright <YEAR> <OWNER>

Permission to use, copy, modify, and/or distribute this software for any purpose with or without fee is hereby granted, provided that the above copyright notice and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
```

## MIT

```
Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
```

## MPL-2.0

```
Mozilla Public License Version 2.0
==================================

1. Definitions
--------------

1.1. "Contributor"
    means each individual or legal entity that creates, contributes to
    the creation of, or owns Covered Software.

1.2. "Contributor Version"
    means the combination of the Contributions of others (if any) used
    by a Contributor and that particular Contributor's Contribution.

1.3. "Contribution"
    means Covered Software of a particular Contributor.

1.4. "Covered Software"
    means Source Code Form to which the initial Contributor has attached
    the notice in Exhibit A, the Executable Form of such Source Code
    Form, and Modifications of such Source Code Form, in each case
    including portions thereof.

1.5. "Incompatible With Secondary Licenses"
    means

    (a) that the initial Contributor has attached the notice described
        in Exhibit B to the Covered Software; or

    (b) that the Covered Software was made available under the terms of
        version 1.1 or earlier of the License, but not also under the
        terms of a Secondary License.

1.6. "Executable Form"
    means any form of the work other than Source Code Form.

1.7. "Larger Work"
    means a work that combines Covered Software with other material, in
    a separate file or files, that is not Covered Software.

1.8. "License"
    means this document.

1.9. "Licensable"
    means having the right to grant, to the maximum extent possible,
    whether at the time of the initial grant or subsequently, any and
    all of the rights conveyed by this License.

1.10. "Modifications"
    means any of the following:

    (a) any file in Source Code Form that results from an addition to,
        deletion from, or modification of the contents of Covered
        Software; or

    (b) any new file in Source Code Form that contains any Covered
        Software.

1.11. "Patent Claims" of a Contributor
    means any patent claim(s), including without limitation, method,
    process, and apparatus claims, in any patent Licensable by such
    Contributor that would be infringed, but for the grant of the
    License, by the making, using, selling, offering for sale, having
    made, import, or transfer of either its Contributions or its
    Contributor Version.

1.12. "Secondary License"
    means either the GNU General Public License, Version 2.0, the GNU
    Lesser General Public License, Version 2.1, the GNU Affero General
    Public License, Version 3.0, or any later versions of those
    licenses.

1.13. "Source Code Form"
    means the form of the work preferred for making modifications.

1.14. "You" (or "Your")
    means an individual or a legal entity exercising rights under this
    License. For legal entities, "You" includes any entity that
    controls, is controlled by, or is under common control with You. For
    purposes of this definition, "control" means (a) the power, direct
    or indirect, to cause the direction or management of such entity,
    whether by contract or otherwise, or (b) ownership of more than
    fifty percent (50%) of the outstanding shares or beneficial
    ownership of such entity.

2. License Grants and Conditions
--------------------------------

2.1. Grants

Each Contributor hereby grants You a world-wide, royalty-free,
non-exclusive license:

(a) under intellectual property rights (other than patent or trademark)
    Licensable by such Contributor to use, reproduce, make available,
    modify, display, perform, distribute, and otherwise exploit its
    Contributions, either on an unmodified basis, with Modifications, or
    as part of a Larger Work; and

(b) under Patent Claims of such Contributor to make, use, sell, offer
    for sale, have made, import, and otherwise transfer either its
    Contributions or its Contributor Version.

2.2. Effective Date

The licenses granted in Section 2.1 with respect to any Contribution
become effective for each Contribution on the date the Contributor first
distributes such Contribution.

2.3. Limitations on Grant Scope

The licenses granted in this Section 2 are the only rights granted under
this License. No additional rights or licenses will be implied from the
distribution or licensing of Covered Software under this License.
Notwithstanding Section 2.1(b) above, no patent license is granted by a
Contributor:

(a) for any code that a Contributor has removed from Covered Software;
    or

(b) for infringements caused by: (i) Your and any other third party's
    modifications of Covered Software, or (ii) the combination of its
    Contributions with other software (except as part of its Contributor
    Version); or

(c) under Patent Claims infringed by Covered Software in the absence of
    its Contributions.

This License does not grant any rights in the trademarks, service marks,
or logos of any Contributor (except as may be necessary to comply with
the notice requirements in Section 3.4).

2.4. Subsequent Licenses

No Contributor makes additional grants as a result of Your choice to
distribute the Covered Software under a subsequent version of this
License (see Section 10.2) or under the terms of a Secondary License (if
permitted under the terms of Section 3.3).

2.5. Representation

Each Contributor represents that the Contributor believes its
Contributions are its original creation(s) or it has sufficient rights
to grant the rights to its Contributions conveyed by this License.

2.6. Fair Use

This License is not intended to limit any rights You have under
applicable copyright doctrines of fair use, fair dealing, or other
equivalents.

2.7. Conditions

Sections 3.1, 3.2, 3.3, and 3.4 are conditions of the licenses granted
in Section 2.1.

3. Responsibilities
-------------------

3.1. Distribution of Source Form

All distribution of Covered Software in Source Code Form, including any
Modifications that You create or to which You contribute, must be under
the terms of this License. You must inform recipients that the Source
Code Form of the Covered Software is governed by the terms of this
License, and how they can obtain a copy of this License. You may not
attempt to alter or restrict the recipients' rights in the Source Code
Form.

3.2. Distribution of Executable Form

If You distribute Covered Software in Executable Form then:

(a) such Covered Software must also be made available in Source Code
    Form, as described in Section 3.1, and You must inform recipients of
    the Executable Form how they can obtain a copy of such Source Code
    Form by reasonable means in a timely manner, at a charge no more
    than the cost of distribution to the recipient; and

(b) You may distribute such Executable Form under the terms of this
    License, or sublicense it under different terms, provided that the
    license for the Executable Form does not attempt to limit or alter
    the recipients' rights in the Source Code Form under this License.

3.3. Distribution of a Larger Work

You may create and distribute a Larger Work under terms of Your choice,
provided that You also comply with the requirements of this License for
the Covered Software. If the Larger Work is a combination of Covered
Software with a work governed by one or more Secondary Licenses, and the
Covered Software is not Incompatible With Secondary Licenses, this
License permits You to additionally distribute such Covered Software
under the terms of such Secondary License(s), so that the recipient of
the Larger Work may, at their option, further distribute the Covered
Software under the terms of either this License or such Secondary
License(s).

3.4. Notices

You may not remove or alter the substance of any license notices
(including copyright notices, patent notices, disclaimers of warranty,
or limitations of liability) contained within the Source Code Form of
the Covered Software, except that You may alter any license notices to
the extent required to remedy known factual inaccuracies.

3.5. Application of Additional Terms

You may choose to offer, and to charge a fee for, warranty, support,
indemnity or liability obligations to one or more recipients of Covered
Software. However, You may do so only on Your own behalf, and not on
behalf of any Contributor. You must make it absolutely clear that any
such warranty, support, indemnity, or liability obligation is offered by
You alone, and You hereby agree to indemnify every Contributor for any
liability incurred by such Contributor as a result of warranty, support,
indemnity or liability terms You offer. You may include additional
disclaimers of warranty and limitations of liability specific to any
jurisdiction.

4. Inability to Comply Due to Statute or Regulation
---------------------------------------------------

If it is impossible for You to comply with any of the terms of this
License with respect to some or all of the Covered Software due to
statute, judicial order, or regulation then You must: (a) comply with
the terms of this License to the maximum extent possible; and (b)
describe the limitations and the code they affect. Such description must
be placed in a text file included with all distributions of the Covered
Software under this License. Except to the extent prohibited by statute
or regulation, such description must be sufficiently detailed for a
recipient of ordinary skill to be able to understand it.

5. Termination
--------------

5.1. The rights granted under this License will terminate automatically
if You fail to comply with any of its terms. However, if You become
compliant, then the rights granted under this License from a particular
Contributor are reinstated (a) provisionally, unless and until such
Contributor explicitly and finally terminates Your grants, and (b) on an
ongoing basis, if such Contributor fails to notify You of the
non-compliance by some reasonable means prior to 60 days after You have
come back into compliance. Moreover, Your grants from a particular
Contributor are reinstated on an ongoing basis if such Contributor
notifies You of the non-compliance by some reasonable means, this is the
first time You have received notice of non-compliance with this License
from such Contributor, and You become compliant prior to 30 days after
Your receipt of the notice.

5.2. If You initiate litigation against any entity by asserting a patent
infringement claim (excluding declaratory judgment actions,
counter-claims, and cross-claims) alleging that a Contributor Version
directly or indirectly infringes any patent, then the rights granted to
You by any and all Contributors for the Covered Software under Section
2.1 of this License shall terminate.

5.3. In the event of termination under Sections 5.1 or 5.2 above, all
end user license agreements (excluding distributors and resellers) which
have been validly granted by You or Your distributors under this License
prior to termination shall survive termination.

************************************************************************
*                                                                      *
*  6. Disclaimer of Warranty                                           *
*  -------------------------                                           *
*                                                                      *
*  Covered Software is provided under this License on an "as is"       *
*  basis, without warranty of any kind, either expressed, implied, or  *
*  statutory, including, without limitation, warranties that the       *
*  Covered Software is free of defects, merchantable, fit for a        *
*  particular purpose or non-infringing. The entire risk as to the     *
*  quality and performance of the Covered Software is with You.        *
*  Should any Covered Software prove defective in any respect, You     *
*  (not any Contributor) assume the cost of any necessary servicing,   *
*  repair, or correction. This disclaimer of warranty constitutes an   *
*  essential part of this License. No use of any Covered Software is   *
*  authorized under this License except under this disclaimer.         *
*                                                                      *
************************************************************************

************************************************************************
*                                                                      *
*  7. Limitation of Liability                                          *
*  --------------------------                                          *
*                                                                      *
*  Under no circumstances and under no legal theory, whether tort      *
*  (including negligence), contract, or otherwise, shall any           *
*  Contributor, or anyone who distributes Covered Software as          *
*  permitted above, be liable to You for any direct, indirect,         *
*  special, incidental, or consequential damages of any character      *
*  including, without limitation, damages for lost profits, loss of    *
*  goodwill, work stoppage, computer failure or malfunction, or any    *
*  and all other commercial damages or losses, even if such party      *
*  shall have been informed of the possibility of such damages. This   *
*  limitation of liability shall not apply to liability for death or   *
*  personal injury resulting from such party's negligence to the       *
*  extent applicable law prohibits such limitation. Some               *
*  jurisdictions do not allow the exclusion or limitation of           *
*  incidental or consequential damages, so this exclusion and          *
*  limitation may not apply to You.                                    *
*                                                                      *
************************************************************************

8. Litigation
-------------

Any litigation relating to this License may be brought only in the
courts of a jurisdiction where the defendant maintains its principal
place of business and such litigation shall be governed by laws of that
jurisdiction, without reference to its conflict-of-law provisions.
Nothing in this Section shall prevent a party's ability to bring
cross-claims or counter-claims.

9. Miscellaneous
----------------

This License represents the complete agreement concerning the subject
matter hereof. If any provision of this License is held to be
unenforceable, such provision shall be reformed only to the extent
necessary to make it enforceable. Any law or regulation which provides
that the language of a contract shall be construed against the drafter
shall not be used to construe this License against a Contributor.

10. Versions of the License
---------------------------

10.1. New Versions

Mozilla Foundation is the license steward. Except as provided in Section
10.3, no one other than the license steward has the right to modify or
publish new versions of this License. Each version will be given a
distinguishing version number.

10.2. Effect of New Versions

You may distribute the Covered Software under the terms of the version
of the License under which You originally received the Covered Software,
or under the terms of any subsequent version published by the license
steward.

10.3. Modified Versions

If you create software not governed by this License, and you want to
create a new license for such software, you may create and use a
modified version of this License if you rename the license and remove
any references to the name of the license steward (except to note that
such modified license differs from this License).

10.4. Distributing Source Code Form that is Incompatible With Secondary
Licenses

If You choose to distribute Source Code Form that is Incompatible With
Secondary Licenses under the terms of this version of the License, the
notice described in Exhibit B of this License must be attached.

Exhibit A - Source Code Form License Notice
-------------------------------------------

  This Source Code Form is subject to the terms of the Mozilla Public
  License, v. 2.0. If a copy of the MPL was not distributed with this
  file, You can obtain one at http://mozilla.org/MPL/2.0/.

If it is not possible or desirable to put the notice in a particular
file, then You may include the notice in a location (such as a LICENSE
file in a relevant directory) where a recipient would be likely to look
for such a notice.

You may add additional accurate notices of copyright ownership.

Exhibit B - "Incompatible With Secondary Licenses" Notice
---------------------------------------------------------

  This Source Code Form is "Incompatible With Secondary Licenses", as
  defined by the Mozilla Public License, v. 2.0.
```

## Unlicense

```
This is free and unencumbered software released into the public domain.

Anyone is free to copy, modify, publish, use, compile, sell, or
distribute this software, either in source code form or as a compiled
binary, for any purpose, commercial or non-commercial, and by any
means.

In jurisdictions that recognize copyright laws, the author or authors
of this software dedicate any and all copyright interest in the
software to the public domain. We make this dedication for the benefit
of the public at large and to the detriment of our heirs and
successors. We intend this dedication to be an overt act of
relinquishment in perpetuity of all present and future rights to this
software under copyright law.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR
OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
OTHER DEALINGS IN THE SOFTWARE.

For more information, please refer to <http://unlicense.org/>
```

## Zlib

```
This software is provided 'as-is', without any express or implied
warranty. In no event will the authors be held liable for any damages
arising from the use of this software.

Permission is granted to anyone to use this software for any purpose,
including commercial applications, and to alter it and redistribute it
freely, subject to the following restrictions:

1. The origin of this software must not be misrepresented; you must not
   claim that you wrote the original software. If you use this software
   in a product, an acknowledgment in the product documentation would be
   appreciated but is not required.
2. Altered source versions must be plainly marked as such, and must not be
   misrepresented as being the original software.
3. This notice may not be removed or altered from any source distribution.
```
